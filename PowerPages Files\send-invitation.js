/**
 * Unified Notification System for Power Pages
 */
const NotificationSystem = {
  config: {
    defaultTimeout: 5000,
    fadeInDuration: 300,
    fadeOutDuration: 300,
    autoHide: true,
    showIcons: true
  },

  icons: {
    success: 'fas fa-check-circle',
    error: 'fas fa-exclamation-circle',
    warning: 'fas fa-exclamation-triangle',
    info: 'fas fa-info-circle',
    loading: 'fas fa-spinner fa-spin'
  },

  show: function(type, message, options = {}) {
    const settings = { ...this.config, ...options };
    let container = this.getNotificationContainer(settings.containerId);
    if (!container) {
      console.warn('Notification container not found');
      return;
    }

    if (settings.clearExisting !== false) {
      this.clear(container);
    }

    const notification = this.createNotification(type, message, settings);
    container.appendChild(notification);
    notification.classList.add('notification-fade-in');

    if (settings.autoHide && settings.timeout > 0) {
      setTimeout(() => {
        this.hide(notification);
      }, settings.timeout || this.config.defaultTimeout);
    }

    this.setAriaLiveRegion(notification, type);
    return notification;
  },

  showSuccess: function(message, options = {}) {
    return this.show('success', message, {
      ...options,
      timeout: options.timeout || 4000,
      title: options.title || 'Success'
    });
  },

  showError: function(message, options = {}) {
    return this.show('error', message, {
      ...options,
      timeout: options.timeout || 0,
      title: options.title || 'Error'
    });
  },

  showWarning: function(message, options = {}) {
    return this.show('warning', message, {
      ...options,
      timeout: options.timeout || 6000,
      title: options.title || 'Warning'
    });
  },

  showInfo: function(message, options = {}) {
    return this.show('info', message, {
      ...options,
      timeout: options.timeout || 5000,
      title: options.title || 'Information'
    });
  },

  showLoading: function(message, options = {}) {
    return this.show('loading', message, {
      ...options,
      timeout: 0,
      title: options.title || 'Loading',
      autoHide: false
    });
  },

  createNotification: function(type, message, settings) {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.setAttribute('role', type === 'error' ? 'alert' : 'status');
    notification.setAttribute('aria-live', type === 'error' ? 'assertive' : 'polite');

    let content = '';
    if (settings.showIcons && this.icons[type]) {
      content += `<div class="notification-icon"><i class="${this.icons[type]}" aria-hidden="true"></i></div>`;
    }

    content += '<div class="notification-content">';
    if (settings.title) {
      content += `<div class="notification-title">${this.escapeHtml(settings.title)}</div>`;
    }
    content += `<div class="notification-message">${this.escapeHtml(message)}</div>`;
    content += '</div>';

    notification.innerHTML = content;
    if (settings.compact) {
      notification.classList.add('notification-compact');
    }

    return notification;
  },

  getNotificationContainer: function(containerId) {
    if (containerId) {
      return document.getElementById(containerId);
    }

    const commonIds = ['notificationContainer', 'messageContainer', 'alertContainer'];
    for (const id of commonIds) {
      const container = document.getElementById(id);
      if (container) return container;
    }

    const jquerySelectors = ['#errorMessage', '#successMessage', '#messageContent'];
    for (const selector of jquerySelectors) {
      const element = $(selector);
      if (element.length > 0) {
        return element[0];
      }
    }

    return this.createDefaultContainer();
  },

  createDefaultContainer: function() {
    const container = document.createElement('div');
    container.id = 'notificationContainer';
    container.className = 'notification-container';

    const mainContent = document.querySelector('.card-body, .container, main, body');
    if (mainContent) {
      mainContent.insertBefore(container, mainContent.firstChild);
      return container;
    }

    return null;
  },

  hide: function(notification) {
    if (!notification || !notification.parentNode) return;
    notification.style.transition = `opacity ${this.config.fadeOutDuration}ms ease`;
    notification.style.opacity = '0';
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, this.config.fadeOutDuration);
  },

  clear: function(container) {
    if (!container) {
      container = this.getNotificationContainer();
    }

    if (container) {
      const notifications = container.querySelectorAll('.notification, .message, .alert');
      notifications.forEach(notification => {
        this.hide(notification);
      });
    }

    $('#errorMessage, #successMessage').hide();
  },

  setAriaLiveRegion: function(notification, type) {
    if (type === 'error') {
      notification.setAttribute('aria-live', 'assertive');
      notification.setAttribute('role', 'alert');
    } else {
      notification.setAttribute('aria-live', 'polite');
      notification.setAttribute('role', 'status');
    }
  },

  escapeHtml: function(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  },

  handleError: function(error, context = {}) {
    console.error('Error in context:', context, error);

    if (error.status) {
      return this.handleHttpError(error, context);
    }

    if (error instanceof TypeError && error.message.includes('fetch')) {
      return this.showError('Network connection failed. Please check your internet connection and try again.', {
        title: 'Connection Error',
        timeout: 0
      });
    }

    if (error.name === 'AbortError' || error.message.includes('timeout')) {
      return this.showError('Request timed out. Please check your connection and try again. If the problem persists, try refreshing the page.', {
        title: 'Request Timeout',
        timeout: 0
      });
    }

    if (error.message.includes('configuration') || error.message.includes('missing')) {
      return this.showError('System configuration error. Please contact support if this persists.', {
        title: 'Configuration Error',
        timeout: 0
      });
    }

    if (error.message.includes('rate limit') || error.message.includes('429')) {
      return this.showWarning(error.message, {
        title: 'Rate Limit',
        timeout: 10000
      });
    }

    if (error.message.includes('401') || error.message.includes('unauthorized')) {
      return this.showError('Authentication failed. Please refresh the page and try again.', {
        title: 'Authentication Error',
        timeout: 0
      });
    }

    const userMessage = error.message || 'An unexpected error occurred. Please try again.';
    return this.showError(userMessage, {
      title: 'Error',
      timeout: 0
    });
  }
};

const SecureConfig = {
  getFunctionUrl(functionName = 'InvitationService') {
    const baseUrl = window.appConfig?.functionUrl;
    if (!baseUrl || baseUrl.includes('ERROR_MISSING')) {
      return null;
    }
    return `${baseUrl}/api/${functionName}`;
  },

  getFunctionKey() {
    const functionKey = window.appConfig?.invitationFunctionKey;
    if (!functionKey || functionKey.includes('ERROR_MISSING')) {
      return null;
    }
    return functionKey;
  },

  buildSecureUrl(functionName, operation) {
    const baseUrl = this.getFunctionUrl(functionName);
    const functionKey = this.getFunctionKey();
    if (!baseUrl || !functionKey) {
      return null;
    }
    return `${baseUrl}?operation=${operation}&code=${functionKey}`;
  }
};

// Add remaining NotificationSystem methods
NotificationSystem.handleHttpError = function(error, context = {}) {
  const status = error.status;

  if (status >= 400 && status < 500) {
    switch (status) {
      case 400:
        return this.showError('Invalid request. Please check your input and try again.', {
          title: 'Invalid Request',
          timeout: 0
        });
      case 401:
        return this.showError('Authentication required. Please refresh the page and try again.', {
          title: 'Authentication Required',
          timeout: 0
        });
      case 403:
        return this.showError('Access denied. You do not have permission to perform this action.', {
          title: 'Access Denied',
          timeout: 0
        });
      case 404:
        return this.showError('Service not found. Please contact support if this persists.', {
          title: 'Service Unavailable',
          timeout: 0
        });
      case 409:
        const conflictMessage = error.message || 'A conflict occurred. Please review your input.';
        if (conflictMessage.toLowerCase().includes('email') &&
            (conflictMessage.toLowerCase().includes('exists') ||
             conflictMessage.toLowerCase().includes('already') ||
             conflictMessage.toLowerCase().includes('duplicate'))) {
          return this.showError('An account with this email address already exists. Please try signing in instead.', {
            title: 'Account Already Exists',
            timeout: 0
          });
        }
        return this.showError(conflictMessage, {
          title: 'Conflict',
          timeout: 0
        });
      case 429:
        return this.showWarning('Too many requests. Please wait a moment before trying again.', {
          title: 'Rate Limit Exceeded',
          timeout: 15000
        });
      default:
        return this.showError('Request failed. Please try again or contact support if the problem persists.', {
          title: 'Request Failed',
          timeout: 0
        });
    }
  }

  if (status >= 500) {
    return this.showError('A server error occurred. Please try again later or contact support.', {
      title: 'Server Error',
      timeout: 0
    });
  }

  return this.showError('An unexpected error occurred. Please try again.', {
    title: 'Unexpected Error',
    timeout: 0
  });
};

NotificationSystem.validateConfiguration = function(requiredConfig = []) {
  const missing = [];
  for (const configKey of requiredConfig) {
    if (!window.appConfig || !window.appConfig[configKey]) {
      missing.push(configKey);
    }
  }

  if (missing.length > 0) {
    this.showError(`Missing configuration: ${missing.join(', ')}. Please contact support.`, {
      title: 'Configuration Error',
      timeout: 0
    });
    return false;
  }

  return true;
};

NotificationSystem.validateDOMElements = function(elementSelectors = []) {
  const missing = [];
  for (const selector of elementSelectors) {
    const element = document.querySelector(selector);
    if (!element) {
      missing.push(selector);
    }
  }

  if (missing.length > 0) {
    console.error('Missing DOM elements:', missing);
    this.showError('Page elements missing. Please refresh the page.', {
      title: 'Page Error',
      timeout: 0
    });
    return false;
  }

  return true;
};

NotificationSystem.checkBrowserCompatibility = function() {
  const requiredFeatures = [];
  
  // Check for fetch API
  if (typeof fetch === 'undefined') {
    requiredFeatures.push('Fetch API');
  }
  
  // Check for Promise support
  if (typeof Promise === 'undefined') {
    requiredFeatures.push('Promise support');
  }
  
  // Check for modern JavaScript features
  if (typeof URLSearchParams === 'undefined') {
    requiredFeatures.push('URL Parameters');
  }
  
  // Check for localStorage/sessionStorage
  try {
    if (typeof localStorage === 'undefined' || typeof sessionStorage === 'undefined') {
      requiredFeatures.push('Local Storage');
    }
  } catch (e) {
    requiredFeatures.push('Local Storage');
  }
  
  // Check for modern DOM methods
  if (!document.querySelector || !document.querySelectorAll) {
    requiredFeatures.push('Modern DOM methods');
  }
  
  if (requiredFeatures.length > 0) {
    // Use basic DOM manipulation for compatibility message
    const message = `Your browser is missing required features: ${requiredFeatures.join(', ')}. Please update to a modern browser (Chrome 60+, Firefox 55+, Safari 12+, Edge 79+) to use this application.`;
    
    // Try to show error using NotificationSystem, fallback to alert
    try {
      this.showError(message, {
        title: 'Browser Compatibility Issue',
        timeout: 0
      });
    } catch (e) {
      // Fallback to basic alert if NotificationSystem fails
      alert('Browser Compatibility Issue: ' + message);
    }
    
    return false;
  }
  
  return true;
};

let currentInvitationData = null;

function initializeConfiguration() {
  const functionUrl = window.appConfig?.functionUrl;
  if (!functionUrl || functionUrl.includes('ERROR_MISSING')) {
    showMessage('Configuration error: Azure Function URL not set', true);
  }
}

const InputSanitizer = {
  sanitizeString(input) {
    if (typeof input !== 'string') return '';
    return input.trim().replace(/[<>\"'&]/g, '').substring(0, 256);
  },

  validateEmail(email) {
    if (!email || typeof email !== 'string') return false;
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email.trim());
  },

  validateName(name) {
    if (!name || typeof name !== 'string') return false;
    const trimmed = name.trim();

    // Length validation
    if (trimmed.length < 1 || trimmed.length > 50) return false;

    // Enhanced character validation - allow letters, spaces, hyphens, apostrophes, and common international characters
    const namePattern = /^[a-zA-ZÀ-ÿĀ-žА-я\u4e00-\u9fff\s'-]+$/;
    if (!namePattern.test(trimmed)) return false;

    // Prevent names that are only special characters or spaces
    if (!/[a-zA-ZÀ-ÿĀ-žА-я\u4e00-\u9fff]/.test(trimmed)) return false;

    // Prevent excessive consecutive special characters
    if (/[-']{3,}/.test(trimmed)) return false;

    // Prevent names starting or ending with special characters
    if (/^[-'\s]|[-'\s]$/.test(trimmed)) return false;

    return true;
  },

  validateNameWithFeedback(name, fieldName) {
    if (!name || typeof name !== 'string') {
      return { valid: false, message: `${fieldName} is required` };
    }

    const trimmed = name.trim();

    if (trimmed.length < 1) {
      return { valid: false, message: `${fieldName} is required` };
    }

    if (trimmed.length > 50) {
      return { valid: false, message: `${fieldName} must be 50 characters or less` };
    }

    const namePattern = /^[a-zA-ZÀ-ÿĀ-žА-я\u4e00-\u9fff\s'-]+$/;
    if (!namePattern.test(trimmed)) {
      return { valid: false, message: `${fieldName} can only contain letters, spaces, hyphens, and apostrophes` };
    }

    if (!/[a-zA-ZÀ-ÿĀ-žА-я\u4e00-\u9fff]/.test(trimmed)) {
      return { valid: false, message: `${fieldName} must contain at least one letter` };
    }

    if (/[-']{3,}/.test(trimmed)) {
      return { valid: false, message: `${fieldName} cannot contain more than 2 consecutive special characters` };
    }

    if (/^[-'\s]|[-'\s]$/.test(trimmed)) {
      return { valid: false, message: `${fieldName} cannot start or end with special characters` };
    }

    return { valid: true, message: '' };
  }
};

const errorMessage = $('#errorMessage');
const successMessage = $('#successMessage');
const invitationForm = $('#invitationForm');
const sendButton = $('#sendButton');
const recentInvitations = $('#recentInvitations');

// Unified notification functions
function showMessage(message, isError = true) {
  const type = isError ? 'error' : 'success';
  NotificationSystem.show(type, message);

  // Also update legacy elements for compatibility
  errorMessage.addClass('d-none');
  successMessage.addClass('d-none');

  if (isError) {
    $('#errorText').text(message);
    errorMessage.removeClass('d-none');
  } else {
    $('#successText').text(message);
    successMessage.removeClass('d-none');
  }
}

function showSuccess(message, timeout = 4000) {
  return NotificationSystem.showSuccess(message, { timeout: timeout });
}

function showError(message) {
  return NotificationSystem.showError(message);
}

function showLoading(message) {
  return NotificationSystem.showLoading(message);
}

function clearMessages() {
  NotificationSystem.clear();
  // Also clear legacy elements
  errorMessage.addClass('d-none');
  successMessage.addClass('d-none');
}

function showLoadingState(message) {
  sendButton.prop('disabled', true).html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> ' + message);
}

function resetLoadingState() {
  sendButton.prop('disabled', false).html('<i class="fas fa-paper-plane me-2"></i>Send Invitation');
}

async function sendInvitation(invitationData) {
  showLoadingState('Sending Invitation...');

  try {
    const secureUrl = SecureConfig.buildSecureUrl('InvitationService', 'invite-user');
    if (!secureUrl) {
      throw new Error('Azure Function configuration missing. Please check Power Pages settings.');
    }

    const requestBody = {
      Email: InputSanitizer.sanitizeString(invitationData.email),
      FirstName: InputSanitizer.sanitizeString(invitationData.firstName),
      LastName: InputSanitizer.sanitizeString(invitationData.lastName),
      ApplicationName: window.appConfig?.applicationName
    };

    const response = await fetch(secureUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Client-Version': '3.0.0-simplified',
        'X-Requested-With': 'XMLHttpRequest'
      },
      body: JSON.stringify(requestBody)
    });

    if (response.status === 429) {
      throw new Error('Too many requests. Please wait a moment before trying again.');
    }

    if (!response.ok) {
      let errorDetails = 'No additional details';
      try {
        const contentType = response.headers.get('content-type');
        if (contentType && contentType.includes('application/json')) {
          const errorData = await response.json();
          errorDetails = errorData.message || errorData.error || JSON.stringify(errorData);
        } else {
          const textResponse = await response.text();
          errorDetails = textResponse || 'Empty response';
        }
      } catch (parseError) {
      }
      throw new Error(`Server error: ${response.status} - ${errorDetails}`);
    }

    const contentType = response.headers.get('content-type');
    if (!contentType || !contentType.includes('application/json')) {
      throw new Error(`Server error: ${response.status} - Expected JSON response`);
    }

    const result = await response.json();

    let success, message, errorCode, retryAfter;

    if (result.data) {
      success = result.data.success;
      message = result.data.message;
      errorCode = result.data.errorCode;
      retryAfter = result.data.retryAfter;
    } else {
      success = result.success;
      message = result.message;
      errorCode = result.errorCode;
      retryAfter = result.retryAfter;
    }

    const isSuccess = response.ok && success === true;

    if (!isSuccess) {
      if (response.status === 429 || errorCode === 'RateLimitExceeded') {
        const retryMessage = retryAfter ? ` Please try again after ${new Date(retryAfter).toLocaleTimeString()}.` : ' Please try again later.';
        throw new Error((message || 'Rate limit exceeded.') + retryMessage);
      }
      throw new Error(message || 'Failed to send invitation');
    }

    return {
      success: true,
      message: message || 'Invitation sent successfully',
      correlationId: result.correlationId
    };
  } catch (error) {
    throw error;
  } finally {
    resetLoadingState();
  }
}

function validateForm() {
  let isValid = true;

  // Clear previous validation states
  $('.form-control').removeClass('is-invalid is-valid');
  $('.invalid-feedback').text('');
  $('.form-group').removeClass('has-error');

  // Email validation
  const email = $('#email').val();
  const emailValid = validateFieldWithVisualFeedback('#email', '#emailError',
    () => InputSanitizer.validateEmail(email),
    'Please enter a valid email address'
  );
  if (!emailValid) isValid = false;

  // First name validation
  const firstName = $('#firstName').val();
  const firstNameValidation = InputSanitizer.validateNameWithFeedback(firstName, 'First name');
  const firstNameValid = validateFieldWithVisualFeedback('#firstName', '#firstNameError',
    () => firstNameValidation.valid,
    firstNameValidation.message
  );
  if (!firstNameValid) isValid = false;

  // Last name validation
  const lastName = $('#lastName').val();
  const lastNameValidation = InputSanitizer.validateNameWithFeedback(lastName, 'Last name');
  const lastNameValid = validateFieldWithVisualFeedback('#lastName', '#lastNameError',
    () => lastNameValidation.valid,
    lastNameValidation.message
  );
  if (!lastNameValid) isValid = false;

  // Show summary notification if validation fails
  if (!isValid) {
    NotificationSystem.showError('Please correct the highlighted fields and try again.', {
      title: 'Form Validation',
      timeout: 5000
    });
  }

  return isValid;
}

function validateFieldWithVisualFeedback(fieldSelector, errorSelector, validationFunction, errorMessage) {
  const field = $(fieldSelector);
  const errorElement = $(errorSelector);
  const isValid = validationFunction();

  if (isValid) {
    field.removeClass('is-invalid').addClass('is-valid');
    field.closest('.form-group').removeClass('has-error');
    errorElement.text('');
    return true;
  } else {
    field.removeClass('is-valid').addClass('is-invalid');
    field.closest('.form-group').addClass('has-error');
    errorElement.text(errorMessage);

    // Focus on first invalid field
    if ($('.is-invalid').length === 1) {
      field.focus();
    }

    return false;
  }
}

function addToRecentInvitations(email, firstName, lastName) {
  const timestamp = new Date().toLocaleString();
  const invitationHtml = `
    <div class="d-flex justify-content-between align-items-center py-1">
      <span><strong>${firstName} ${lastName}</strong> (${email})</span>
      <small class="text-muted">${timestamp}</small>
    </div>
  `;
  
  if (recentInvitations.text().includes('No recent invitations')) {
    recentInvitations.html(invitationHtml);
  } else {
    recentInvitations.prepend(invitationHtml);
  }
  
  const invitations = recentInvitations.children();
  if (invitations.length > 5) {
    invitations.slice(5).remove();
  }
}

function initializeFormHandlers() {
  invitationForm.submit(async function(event) {
    event.preventDefault();
    showMessage('', false);

    try {
      if (!validateForm()) {
        return;
      }

      const invitationData = {
        email: $('#email').val(),
        firstName: $('#firstName').val(),
        lastName: $('#lastName').val()
      };

      currentInvitationData = invitationData;

      const result = await sendInvitation(invitationData);

      if (result.success) {
        showMessage(`Invitation sent successfully to ${invitationData.email}!`, false);
        addToRecentInvitations(invitationData.email, invitationData.firstName, invitationData.lastName);
        invitationForm[0].reset();
        currentInvitationData = null;
      }

    } catch (error) {
      NotificationSystem.handleError(error, {
        operation: 'send-invitation',
        email: $('#email').val()
      });
    }
  });
}



$(document).ready(function() {
  // Check browser compatibility first
  if (!NotificationSystem.checkBrowserCompatibility()) {
    return; // Stop initialization if browser is incompatible
  }

  initializeConfiguration();
  initializeFormHandlers();
});
