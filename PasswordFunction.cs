using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Graph;
using Microsoft.Graph.Models;
using System.Net;
using System.Text.Json;
using System.ComponentModel.DataAnnotations;
using PasswordHistoryValidator.Services;
using PasswordHistoryValidator.Shared;

namespace PasswordHistoryValidator;

public partial class PasswordFunction
{
    private readonly ILogger<PasswordFunction> _logger;
    private readonly PasswordHistoryService _passwordHistoryService;
    private readonly RateLimitService _rateLimitService;
    private readonly ResetTokenManager _resetTokenManager;
    private readonly EmailService _emailService;
    private readonly GraphServiceClient _graphServiceClient;
    private readonly JsonSerializerOptions _jsonOptions;

    public PasswordFunction(
        ILogger<PasswordFunction> logger,
        PasswordHistoryService passwordHistoryService,
        RateLimitService rateLimitService,
        ResetTokenManager resetTokenManager,
        EmailService emailService,
        GraphServiceClient graphServiceClient,
        JsonSerializerOptions jsonOptions)
    {
        _logger = logger;
        _passwordHistoryService = passwordHistoryService;
        _rateLimitService = rateLimitService;
        _resetTokenManager = resetTokenManager;
        _emailService = emailService;
        _graphServiceClient = graphServiceClient;
        _jsonOptions = jsonOptions;
    }


    [Function("PasswordService")]
    public async Task<HttpResponseData> Run(
        [HttpTrigger(AuthorizationLevel.Function, "post", "options")] HttpRequestData req,
        CancellationToken cancellationToken)
    {
        var correlationId = Utilities.GenerateCorrelationId();

        if (req.Method.Equals("OPTIONS", StringComparison.OrdinalIgnoreCase))
        {
            return HttpResponseHelper.CreateCorsResponse(req);
        }

        try
        {
            var operation = req.Query["operation"];
            if (string.IsNullOrEmpty(operation))
            {
                return await HttpResponseHelper.CreateErrorResponse(req, "Operation parameter required", correlationId, _jsonOptions);
            }

            return operation switch
            {
                "validate" => await HandlePasswordValidation(req, correlationId, cancellationToken),
                "update-history" => await HandleHistoryUpdate(req, correlationId, cancellationToken),
                "reset-initiate" => await HandleResetInitiate(req, correlationId, cancellationToken),
                "reset-complete" => await HandleResetComplete(req, correlationId, cancellationToken),
                "validate-reset-token" => await HandleValidateResetToken(req, correlationId, cancellationToken),
                _ => await HttpResponseHelper.CreateErrorResponse(req, $"Invalid operation: {operation}", correlationId, _jsonOptions)
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Password service error [CorrelationId: {CorrelationId}]", correlationId);
            return await HttpResponseHelper.CreateErrorResponse(req, "Service error", correlationId, _jsonOptions);
        }
    }



    private async Task<HttpResponseData> HandlePasswordValidation(HttpRequestData req, string correlationId, CancellationToken cancellationToken)
    {
        var data = await JsonSerializer.DeserializeAsync<PasswordRequest>(req.Body, _jsonOptions, cancellationToken);

        if (data == null)
            return await HttpResponseHelper.CreateErrorResponse(req, "Invalid request data", correlationId, _jsonOptions);

        var applicationName = data.ApplicationName ?? "Default Application";

        var validationError = await HttpResponseHelper.ValidateRequestData(req, data, correlationId, _jsonOptions);
        if (validationError != null)
            return validationError;

        var rateLimitResponse = await HttpResponseHelper.CheckRateLimit(req, _rateLimitService, "validate", correlationId, _jsonOptions, cancellationToken);
        if (rateLimitResponse != null)
            return rateLimitResponse;

        if (string.IsNullOrEmpty(data.UserId) || string.IsNullOrEmpty(data.NewPassword))
            return await HttpResponseHelper.CreateErrorResponse(req, "UserId and NewPassword are required for validation", correlationId, _jsonOptions);

        var validationResult = await _passwordHistoryService.ValidatePasswordAgainstHistoryAsync(
            applicationName, data.UserId, data.NewPassword, cancellationToken);

        if (!validationResult.Success)
        {
            return await HttpResponseHelper.CreateErrorResponse(req, validationResult.ErrorMessage, correlationId, _jsonOptions, 
                validationResult.ErrorMessage.Contains("used recently") ? HttpStatusCode.Conflict : HttpStatusCode.BadRequest);
        }

        var updateResult = await _passwordHistoryService.UpdatePasswordHistoryAsync(
            applicationName, data.UserId, data.NewPassword, cancellationToken);

        if (!updateResult.Success)
        {
            _logger.LogWarning("Failed to update password history for user {UserId}: {Error} [CorrelationId: {CorrelationId}]", 
                data.UserId, updateResult.ErrorMessage, correlationId);
            return await HttpResponseHelper.CreateJsonResponse(req, new
            {
                success = true,
                message = "Password validation successful. History update will be retried."
            }, HttpStatusCode.OK, correlationId, _jsonOptions);
        }

        return await HttpResponseHelper.CreateJsonResponse(req, new
        {
            success = true,
            message = "Password validation successful"
        }, HttpStatusCode.OK, correlationId, _jsonOptions);
    }

    private async Task<HttpResponseData> HandleHistoryUpdate(HttpRequestData req, string correlationId, CancellationToken cancellationToken)
    {
        var data = await JsonSerializer.DeserializeAsync<PasswordRequest>(req.Body, _jsonOptions, cancellationToken);

        if (data == null)
            return await HttpResponseHelper.CreateErrorResponse(req, "Invalid request data", correlationId, _jsonOptions);

        var validationError = await HttpResponseHelper.ValidateRequestData(req, data, correlationId, _jsonOptions);
        if (validationError != null)
            return validationError;

        var rateLimitResponse = await HttpResponseHelper.CheckRateLimit(req, _rateLimitService, "update-history", correlationId, _jsonOptions, cancellationToken);
        if (rateLimitResponse != null)
            return rateLimitResponse;

        var userId = !string.IsNullOrEmpty(data.UserId) ? data.UserId : data.Email ?? string.Empty;

        var updateResult = await _passwordHistoryService.UpdatePasswordHistoryAsync(
            data.ApplicationName ?? "Default Application", userId, data.NewPassword ?? string.Empty, cancellationToken);

        if (!updateResult.Success)
        {
            _logger.LogWarning("Failed to update password history for user {UserId}: {Error} [CorrelationId: {CorrelationId}]", 
                userId, updateResult.ErrorMessage, correlationId);
            return await HttpResponseHelper.CreateErrorResponse(req, updateResult.ErrorMessage, correlationId, _jsonOptions);
        }

        return await HttpResponseHelper.CreateJsonResponse(req, new 
        { 
            success = true, 
            message = "Password history updated successfully" 
        }, HttpStatusCode.OK, correlationId, _jsonOptions);
    }

    private async Task<HttpResponseData> HandleResetInitiate(HttpRequestData req, string correlationId, CancellationToken cancellationToken)
    {
        var data = await JsonSerializer.DeserializeAsync<PasswordRequest>(req.Body, _jsonOptions, cancellationToken);

        if (data == null)
            return await HttpResponseHelper.CreateErrorResponse(req, "Invalid request data", correlationId, _jsonOptions);

        var validationError = await HttpResponseHelper.ValidateRequestData(req, data, correlationId, _jsonOptions);
        if (validationError != null)
            return validationError;

        // Normalize email for consistent processing
        data.Email = Utilities.NormalizeEmail(data.Email);

        var rateLimitResponse = await HttpResponseHelper.CheckRateLimit(req, _rateLimitService, "reset-initiate", correlationId, _jsonOptions, cancellationToken);
        if (rateLimitResponse != null)
            return rateLimitResponse;

        var emailEsc = Utilities.EscapeODataString(data.Email ?? string.Empty);
        var appEsc = Utilities.EscapeODataString(data.ApplicationName ?? string.Empty);
        var users = await _graphServiceClient.Users
            .GetAsync(requestConfiguration =>
            {
                requestConfiguration.QueryParameters.Filter =
                    $"(mail eq '{emailEsc}' or userPrincipalName eq '{emailEsc}' or proxyAddresses/any(c:c eq 'SMTP:{emailEsc}')) and department eq '{appEsc}'";
                requestConfiguration.QueryParameters.Select = new[] { "id" };
                requestConfiguration.QueryParameters.Top = 1;
            }, cancellationToken);
        var userExists = users?.Value?.Count > 0;

        if (userExists)
        {
            await SendResetEmailAsync(data, correlationId);
        }

        return await HttpResponseHelper.CreateJsonResponse(req, new
        {
            success = true,
            message = "If an account with that email exists, you will receive a password reset link shortly. Please check your email."
        }, HttpStatusCode.OK, correlationId, _jsonOptions);
    }

    private async Task SendResetEmailAsync(PasswordRequest data, string correlationId)
    {
        try
        {
            var resetToken = _resetTokenManager.GenerateSecureToken();
            var verificationCode = _resetTokenManager.StoreResetToken(data.ApplicationName, data.Email!, resetToken);
            await _resetTokenManager.SendResetEmail(data.Email!, resetToken, verificationCode, data.ApplicationName, correlationId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending reset email to {Email} [CorrelationId: {CorrelationId}]", data.Email, correlationId);
        }
    }

    private async Task<HttpResponseData> HandleValidateResetToken(HttpRequestData req, string correlationId, CancellationToken cancellationToken)
    {
        var data = await JsonSerializer.DeserializeAsync<TokenValidationRequest>(req.Body, _jsonOptions, cancellationToken);

        if (data == null || string.IsNullOrEmpty(data.Token))
            return await HttpResponseHelper.CreateErrorResponse(req, "Token is required", correlationId, _jsonOptions);

        var rateLimitResponse = await HttpResponseHelper.CheckRateLimit(req, _rateLimitService, "validate-reset-token", correlationId, _jsonOptions, cancellationToken);
        if (rateLimitResponse != null)
            return rateLimitResponse;

        var (isValid, tokenData, errorMessage) = _resetTokenManager.ValidateResetTokenOnly(data.Token);

        if (!isValid || tokenData == null)
        {
            _logger.LogWarning("Reset token validation failed: {ErrorMessage} [CorrelationId: {CorrelationId}]",
                errorMessage, correlationId);
            return await HttpResponseHelper.CreateJsonResponse(req, new
            {
                success = false,
                message = errorMessage ?? "Invalid or expired reset token"
            }, HttpStatusCode.Unauthorized, correlationId, _jsonOptions);
        }

        _logger.LogInformation("Reset token validation successful for {Email} [CorrelationId: {CorrelationId}]",
            tokenData.Email, correlationId);

        return await HttpResponseHelper.CreateJsonResponse(req, new
        {
            success = true,
            message = "Reset token is valid",
            data = new
            {
                email = tokenData.Email,
                applicationId = tokenData.ApplicationId,
                expiresUtc = tokenData.ExpiresUtc
            }
        }, HttpStatusCode.OK, correlationId, _jsonOptions);
    }

    private async Task<HttpResponseData> HandleResetComplete(HttpRequestData req, string correlationId, CancellationToken cancellationToken)
    {
        var data = await JsonSerializer.DeserializeAsync<PasswordRequest>(req.Body, _jsonOptions, cancellationToken);

        if (data == null)
            return await HttpResponseHelper.CreateErrorResponse(req, "Invalid request data", correlationId, _jsonOptions);

        bool hasToken = !string.IsNullOrEmpty(data.Token);
        bool hasVerificationCode = !string.IsNullOrEmpty(data.VerificationCode);
        if (!(hasToken && hasVerificationCode))
            return await HttpResponseHelper.CreateErrorResponse(req, "Token and verification code are required for password reset", correlationId, _jsonOptions);

        var applicationName = data.ApplicationName ?? "Default Application";

        var (isValid, tokenData, errorMessage) = _resetTokenManager.ValidateResetToken(data.Token!, data.VerificationCode!);
        if (!isValid)
        {
            _logger.LogWarning("Invalid or expired reset token/verification code for application {ApplicationName} [CorrelationId: {CorrelationId}]",
                applicationName, correlationId);
            return await HttpResponseHelper.CreateErrorResponse(req, errorMessage, correlationId, _jsonOptions);
        }
        // Normalize email for consistent processing
        data.Email = Utilities.NormalizeEmail(tokenData!.Email);

        var rateLimitResponse = await HttpResponseHelper.CheckRateLimit(req, _rateLimitService, "reset-complete", correlationId, _jsonOptions, cancellationToken);
        if (rateLimitResponse != null)
            return rateLimitResponse;

        string userEmail = data.Email!;
        var emailEsc2 = Utilities.EscapeODataString(userEmail);
        var appEsc2 = Utilities.EscapeODataString(applicationName);
        var filter = $"(mail eq '{emailEsc2}' or userPrincipalName eq '{emailEsc2}' or proxyAddresses/any(c:c eq 'SMTP:{emailEsc2}')) and department eq '{appEsc2}'";

        var users = await _graphServiceClient.Users
            .GetAsync(requestConfiguration =>
            {
                requestConfiguration.QueryParameters.Filter = filter;
                requestConfiguration.QueryParameters.Select = new[] { "id", "mail", "userPrincipalName", "displayName", "department" };
                requestConfiguration.QueryParameters.Top = 1;
            }, cancellationToken);

        var user = users?.Value?.FirstOrDefault();
        if (user == null)
        {
            _logger.LogWarning("Failed to resolve email {Email} to User ID for application {ApplicationName} [CorrelationId: {CorrelationId}]",
                userEmail, applicationName, correlationId);
            return await HttpResponseHelper.CreateErrorResponse(req, "User not found with the provided email address in the specified application context.", correlationId, _jsonOptions);
        }

        string resolvedUserId = user.Id ?? string.Empty;

        if (string.IsNullOrEmpty(data.NewPassword))
            return await HttpResponseHelper.CreateErrorResponse(req, "NewPassword is required", correlationId, _jsonOptions);

        var passwordValidation = await _passwordHistoryService.ValidatePasswordAgainstHistoryAsync(
            applicationName, resolvedUserId, data.NewPassword, cancellationToken);

        if (!passwordValidation.Success)
        {
            return await HttpResponseHelper.CreateErrorResponse(req, passwordValidation.ErrorMessage, correlationId, _jsonOptions, 
                passwordValidation.ErrorMessage.Contains("used recently") ? HttpStatusCode.Conflict : HttpStatusCode.BadRequest);
        }

        var userUpdate = new User
        {
            PasswordProfile = new PasswordProfile
            {
                Password = data.NewPassword,
                ForceChangePasswordNextSignIn = false
            }
        };
        await _graphServiceClient.Users[resolvedUserId].PatchAsync(userUpdate, requestConfiguration => { }, cancellationToken);

        if (!string.IsNullOrEmpty(data.Token))
        {
            var tokenMarked = _resetTokenManager.MarkTokenAsUsed(data.Token);
            if (!tokenMarked)
            {
                _logger.LogWarning("Failed to mark reset token as used for {Email} [CorrelationId: {CorrelationId}]", userEmail, correlationId);
            }
        }

        await HandlePostPasswordUpdateTasksAsync(data, userEmail, resolvedUserId, correlationId, cancellationToken);

        var successMessage = "Password reset successfully. You will be logged out and need to sign in with your new password.";

        return await HttpResponseHelper.CreateJsonResponse(req, new
        {
            success = true,
            message = successMessage,
            email = userEmail,
            requiresLogout = true
        }, HttpStatusCode.OK, correlationId, _jsonOptions);
    }

    private async Task HandlePostPasswordUpdateTasksAsync(PasswordRequest data, string userEmail, string resolvedUserId, string correlationId, CancellationToken cancellationToken)
    {
        try
        {
            var historyUpdate = await _passwordHistoryService.UpdatePasswordHistoryAsync(
                data.ApplicationName, resolvedUserId, data.NewPassword ?? string.Empty, cancellationToken);

            if (!historyUpdate.Success)
            {
                _logger.LogWarning("Failed to update password history for user {UserId}: {Error} [CorrelationId: {CorrelationId}]", 
                    resolvedUserId, historyUpdate.ErrorMessage, correlationId);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating password history for {Email} [CorrelationId: {CorrelationId}]", userEmail, correlationId);
        }

        try
        {
            await _emailService.SendPasswordChangedNotificationAsync(userEmail, correlationId, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending password changed notification to {Email} [CorrelationId: {CorrelationId}]", userEmail, correlationId);
        }
    }

}
