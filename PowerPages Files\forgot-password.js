/**
 * Unified Notification System for Power Pages
 * Provides consistent notification display across all authentication flows
 */
const NotificationSystem = {
  // Default configuration
  config: {
    defaultTimeout: 5000,
    fadeInDuration: 300,
    fadeOutDuration: 300,
    autoHide: true,
    showIcons: true
  },

  // Icon mappings for different notification types
  icons: {
    success: 'fas fa-check-circle',
    error: 'fas fa-exclamation-circle',
    warning: 'fas fa-exclamation-triangle',
    info: 'fas fa-info-circle',
    loading: 'fas fa-spinner fa-spin'
  },

  /**
   * Show a notification with specified type and content
   */
  show: function(type, message, options = {}) {
    const settings = { ...this.config, ...options };

    // Find or create notification container
    let container = this.getNotificationContainer(settings.containerId);
    if (!container) {
      console.warn('Notification container not found');
      return;
    }

    // Clear existing notifications if specified
    if (settings.clearExisting !== false) {
      this.clear(container);
    }

    // Create notification element
    const notification = this.createNotification(type, message, settings);

    // Add to container
    container.appendChild(notification);

    // Add fade-in animation
    notification.classList.add('notification-fade-in');

    // Set up auto-hide if enabled
    if (settings.autoHide && settings.timeout > 0) {
      setTimeout(() => {
        this.hide(notification);
      }, settings.timeout || this.config.defaultTimeout);
    }

    // Set ARIA live region for accessibility
    this.setAriaLiveRegion(notification, type);

    return notification;
  },

  /**
   * Show success notification
   */
  showSuccess: function(message, options = {}) {
    return this.show('success', message, {
      ...options,
      timeout: options.timeout || 4000,
      title: options.title || 'Success'
    });
  },

  /**
   * Show error notification
   */
  showError: function(message, options = {}) {
    return this.show('error', message, {
      ...options,
      timeout: options.timeout || 0, // Don't auto-hide errors
      title: options.title || 'Error'
    });
  },

  /**
   * Show warning notification
   */
  showWarning: function(message, options = {}) {
    return this.show('warning', message, {
      ...options,
      timeout: options.timeout || 6000,
      title: options.title || 'Warning'
    });
  },

  /**
   * Show info notification
   */
  showInfo: function(message, options = {}) {
    return this.show('info', message, {
      ...options,
      timeout: options.timeout || 5000,
      title: options.title || 'Information'
    });
  },

  /**
   * Show loading notification
   */
  showLoading: function(message, options = {}) {
    return this.show('loading', message, {
      ...options,
      timeout: 0, // Don't auto-hide loading
      title: options.title || 'Loading',
      autoHide: false
    });
  },

  /**
   * Create notification DOM element
   */
  createNotification: function(type, message, settings) {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;

    // Add role for accessibility
    notification.setAttribute('role', type === 'error' ? 'alert' : 'status');
    notification.setAttribute('aria-live', type === 'error' ? 'assertive' : 'polite');

    let content = '';

    // Add icon if enabled
    if (settings.showIcons && this.icons[type]) {
      content += `<div class="notification-icon"><i class="${this.icons[type]}" aria-hidden="true"></i></div>`;
    }

    // Add content wrapper
    content += '<div class="notification-content">';

    // Add title if provided
    if (settings.title) {
      content += `<div class="notification-title">${this.escapeHtml(settings.title)}</div>`;
    }

    // Add message
    content += `<div class="notification-message">${this.escapeHtml(message)}</div>`;
    content += '</div>';

    notification.innerHTML = content;

    // Add compact class if specified
    if (settings.compact) {
      notification.classList.add('notification-compact');
    }

    return notification;
  },

  /**
   * Get or create notification container
   */
  getNotificationContainer: function(containerId) {
    // Try specific container ID first
    if (containerId) {
      return document.getElementById(containerId);
    }

    // Try common container IDs
    const commonIds = ['notificationContainer', 'messageContainer', 'alertContainer'];
    for (const id of commonIds) {
      const container = document.getElementById(id);
      if (container) return container;
    }

    // Try jQuery selectors for existing patterns
    const jquerySelectors = ['#errorMessage', '#successMessage', '#messageContent'];
    for (const selector of jquerySelectors) {
      const element = $(selector);
      if (element.length > 0) {
        return element[0];
      }
    }

    // Create default container if none found
    return this.createDefaultContainer();
  }
};

const SecureConfig = {
  getFunctionUrl(functionName = 'PasswordService') {
    const baseUrl = window.appConfig?.functionUrl;
    if (!baseUrl) {
      return null;
    }
    return `${baseUrl}/api/${functionName}`;
  },

  getFunctionKey() {
    const functionKey = window.appConfig?.passwordFunctionKey;
    if (!functionKey || functionKey.includes('ERROR_MISSING')) {
      return null;
    }
    return functionKey;
  },

  buildSecureUrl(functionName, operation) {
    const baseUrl = this.getFunctionUrl(functionName);
    const functionKey = this.getFunctionKey();
    if (!baseUrl || !functionKey) {
      return null;
    }
    return `${baseUrl}?operation=${operation}&code=${functionKey}`;
  }
};

// Continue NotificationSystem methods
NotificationSystem.createDefaultContainer = function() {
  const container = document.createElement('div');
  container.id = 'notificationContainer';
  container.className = 'notification-container';

  // Insert at top of main content area
  const mainContent = document.querySelector('.card-body, .container, main, body');
  if (mainContent) {
    mainContent.insertBefore(container, mainContent.firstChild);
    return container;
  }

  return null;
};

NotificationSystem.hide = function(notification) {
  if (!notification || !notification.parentNode) return;

  notification.style.transition = `opacity ${this.config.fadeOutDuration}ms ease`;
  notification.style.opacity = '0';

  setTimeout(() => {
    if (notification.parentNode) {
      notification.parentNode.removeChild(notification);
    }
  }, this.config.fadeOutDuration);
};

NotificationSystem.clear = function(container) {
  if (!container) {
    container = this.getNotificationContainer();
  }

  if (container) {
    const notifications = container.querySelectorAll('.notification, .message, .alert');
    notifications.forEach(notification => {
      this.hide(notification);
    });
  }

  // Also clear jQuery-based notifications
  $('#errorMessage, #successMessage').hide();
};

NotificationSystem.setAriaLiveRegion = function(notification, type) {
  if (type === 'error') {
    notification.setAttribute('aria-live', 'assertive');
    notification.setAttribute('role', 'alert');
  } else {
    notification.setAttribute('aria-live', 'polite');
    notification.setAttribute('role', 'status');
  }
};

NotificationSystem.escapeHtml = function(text) {
  const div = document.createElement('div');
  div.textContent = text;
  return div.innerHTML;
};

NotificationSystem.handleError = function(error, context = {}) {
  console.error('Error in context:', context, error);

  // HTTP Status Code specific handling
  if (error.status) {
    return this.handleHttpError(error, context);
  }

  // Network/Fetch errors
  if (error instanceof TypeError && error.message.includes('fetch')) {
    return this.showError('Network connection failed. Please check your internet connection and try again.', {
      title: 'Connection Error',
      timeout: 0
    });
  }

  // Timeout errors with retry guidance
  if (error.name === 'AbortError' || error.message.includes('timeout')) {
    return this.showError('Request timed out. Please check your connection and try again. If the problem persists, try refreshing the page.', {
      title: 'Request Timeout',
      timeout: 0
    });
  }

  // Configuration errors
  if (error.message.includes('configuration') || error.message.includes('missing')) {
    return this.showError('System configuration error. Please contact support if this persists.', {
      title: 'Configuration Error',
      timeout: 0
    });
  }

  // Rate limiting
  if (error.message.includes('rate limit') || error.message.includes('429')) {
    return this.showWarning(error.message, {
      title: 'Rate Limit',
      timeout: 10000
    });
  }

  // Authentication errors
  if (error.message.includes('401') || error.message.includes('unauthorized')) {
    return this.showError('Authentication failed. Please refresh the page and try again.', {
      title: 'Authentication Error',
      timeout: 0
    });
  }

  // Default error handling
  const userMessage = error.message || 'An unexpected error occurred. Please try again.';
  return this.showError(userMessage, {
    title: 'Error',
    timeout: 0
  });
};

NotificationSystem.handleHttpError = function(error, context = {}) {
  const status = error.status;

  // Client errors (4xx)
  if (status >= 400 && status < 500) {
    switch (status) {
      case 400:
        return this.showError('Invalid request. Please check your input and try again.', {
          title: 'Invalid Request',
          timeout: 0
        });
      case 401:
        return this.showError('Authentication required. Please refresh the page and try again.', {
          title: 'Authentication Required',
          timeout: 0
        });
      case 403:
        return this.showError('Access denied. You do not have permission to perform this action.', {
          title: 'Access Denied',
          timeout: 0
        });
      case 404:
        return this.showError('Service not found. Please contact support if this persists.', {
          title: 'Service Unavailable',
          timeout: 0
        });
      case 429:
        return this.showWarning('Too many requests. Please wait a moment before trying again.', {
          title: 'Rate Limit Exceeded',
          timeout: 15000
        });
      default:
        return this.showError('Request failed. Please try again or contact support if the problem persists.', {
          title: 'Request Failed',
          timeout: 0
        });
    }
  }

  // Server errors (5xx)
  if (status >= 500) {
    return this.showError('A server error occurred. Please try again later or contact support.', {
      title: 'Server Error',
      timeout: 0
    });
  }

  // Fallback
  return this.showError('An unexpected error occurred. Please try again.', {
    title: 'Unexpected Error',
    timeout: 0
  });
};

NotificationSystem.validateConfiguration = function(requiredConfig = []) {
  const missing = [];

  for (const configKey of requiredConfig) {
    if (!window.appConfig || !window.appConfig[configKey]) {
      missing.push(configKey);
    }
  }

  if (missing.length > 0) {
    this.showError(`Missing configuration: ${missing.join(', ')}. Please contact support.`, {
      title: 'Configuration Error',
      timeout: 0
    });
    return false;
  }

  return true;
};

NotificationSystem.validateDOMElements = function(elementSelectors = []) {
  const missing = [];

  for (const selector of elementSelectors) {
    const element = document.querySelector(selector);
    if (!element) {
      missing.push(selector);
    }
  }

  if (missing.length > 0) {
    console.error('Missing DOM elements:', missing);
    this.showError('Page elements missing. Please refresh the page.', {
      title: 'Page Error',
      timeout: 0
    });
    return false;
  }

  return true;
};

NotificationSystem.checkBrowserCompatibility = function() {
  const requiredFeatures = [];
  
  // Check for fetch API
  if (typeof fetch === 'undefined') {
    requiredFeatures.push('Fetch API');
  }
  
  // Check for Promise support
  if (typeof Promise === 'undefined') {
    requiredFeatures.push('Promise support');
  }
  
  // Check for modern JavaScript features
  if (typeof URLSearchParams === 'undefined') {
    requiredFeatures.push('URL Parameters');
  }
  
  // Check for localStorage/sessionStorage
  try {
    if (typeof localStorage === 'undefined' || typeof sessionStorage === 'undefined') {
      requiredFeatures.push('Local Storage');
    }
  } catch (e) {
    requiredFeatures.push('Local Storage');
  }
  
  // Check for modern DOM methods
  if (!document.querySelector || !document.querySelectorAll) {
    requiredFeatures.push('Modern DOM methods');
  }
  
  if (requiredFeatures.length > 0) {
    // Use basic DOM manipulation for compatibility message
    const message = `Your browser is missing required features: ${requiredFeatures.join(', ')}. Please update to a modern browser (Chrome 60+, Firefox 55+, Safari 12+, Edge 79+) to use this application.`;
    
    // Try to show error using NotificationSystem, fallback to alert
    try {
      this.showError(message, {
        title: 'Browser Compatibility Issue',
        timeout: 0
      });
    } catch (e) {
      // Fallback to basic alert if NotificationSystem fails
      alert('Browser Compatibility Issue: ' + message);
    }
    
    return false;
  }
  
  return true;
};

const PASSWORD_SERVICE_URL = SecureConfig.getFunctionUrl('PasswordService');

const APPLICATION_ID = window.appConfig?.applicationId || "default-application";
const APPLICATION_NAME = window.appConfig?.applicationName ||
                        document.getElementById('applicationName')?.value ||
                        "ApplicationNameNotSet";

if (!PASSWORD_SERVICE_URL) {
  console.error("PasswordService URL not configured");
  NotificationSystem.showError('System configuration missing. Please contact support.', {
    title: 'Configuration Error',
    timeout: 0
  });
}

const InputSanitizer = {
  sanitizeInput(input) {
    if (typeof input !== 'string') return '';
    return input.trim().replace(/[<>\"'&]/g, '').substring(0, 256);
  },

  sanitizeEmail(input) {
    if (typeof input !== 'string') return '';
    // Normalize email to lowercase for consistent processing
    return input.trim().toLowerCase().replace(/[<>\"'&]/g, '').substring(0, 256);
  },

  validateEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email) && email.length <= 254;
  }
};



const errorMessageDiv = $('#errorMessage');
const successMessageDiv = $('#successMessage');
const resetButton = $('#resetButton');
const forgotPasswordForm = $('#forgotPasswordForm');
const emailInput = $('#email');

// Unified notification functions
function showMessage(message, isError = true, timeout = 0) {
  const type = isError ? 'error' : 'success';
  const options = timeout > 0 ? { timeout: timeout } : {};
  return NotificationSystem.show(type, message, options);
}

function clearMessages() {
  NotificationSystem.clear();
  // Also clear legacy elements for compatibility
  errorMessageDiv.hide();
  successMessageDiv.hide();
}

function showSuccess(message, timeout = 4000) {
  return NotificationSystem.showSuccess(message, { timeout: timeout });
}

function showError(message) {
  return NotificationSystem.showError(message);
}

function showLoading(message) {
  return NotificationSystem.showLoading(message);
}

function validateEmail() {
  const email = emailInput.val().trim();
  const emailError = $('#emailError');

  // Clear previous validation state
  emailInput.removeClass('is-invalid is-valid');
  emailInput.closest('.form-group').removeClass('has-error');

  if (!email) {
    emailError.text('Email address is required');
    emailInput.addClass('is-invalid');
    emailInput.closest('.form-group').addClass('has-error');
    emailInput.focus();
    return false;
  }

  if (!InputSanitizer.validateEmail(email)) {
    emailError.text('Please enter a valid email address');
    emailInput.addClass('is-invalid');
    emailInput.closest('.form-group').addClass('has-error');
    emailInput.focus();
    return false;
  }

  emailError.text('');
  emailInput.removeClass('is-invalid').addClass('is-valid');
  return true;
}

function validateForm() {
  return validateEmail();
}

async function initiatePasswordReset(email) {
  try {
    const sanitizedEmail = InputSanitizer.sanitizeEmail(email);

    if (!InputSanitizer.validateEmail(sanitizedEmail)) {
      throw new Error('Invalid email address');
    }

    const secureUrl = SecureConfig.buildSecureUrl('PasswordService', 'reset-initiate');
    if (!secureUrl) {
      throw new Error('Azure Function configuration missing. Please check Power Pages settings.');
    }



    const response = await fetch(secureUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Client-Version': '3.0.0-simplified',
        'X-Requested-With': 'XMLHttpRequest'
      },
      body: JSON.stringify({
        email: sanitizedEmail,
        applicationName: APPLICATION_NAME
      })
    });

    if (response.status === 429) {
      const responseText = await response.text();
      let result;
      try {
        result = JSON.parse(responseText);
        if (result.retryAfter) {
          const retryAfter = new Date(result.retryAfter);
          const waitTime = Math.ceil((retryAfter - new Date()) / 1000);
          throw new Error(`Too many requests. Please wait ${waitTime} seconds before trying again.`);
        }
      } catch (parseError) {
        throw new Error('Too many requests. Please wait a moment before trying again.');
      }
      throw new Error('Too many requests. Please wait a moment before trying again.');
    }

    const responseText = await response.text();
    let result;
    if (responseText) {
      try {
        result = JSON.parse(responseText);
      } catch (parseError) {
        throw new Error('Invalid response from server. Please try again.');
      }
    } else {
      throw new Error('Empty response from server. Please try again.');
    }

    // Always return success message for security (prevents email enumeration)
    return {
      success: true,
      message: result?.message || "If an account with that email exists, you will receive a reset link shortly."
    };

  } catch (error) {
    throw error;
  }
}

function initializeFormHandlers() {
  emailInput.blur(function() {
    validateEmail();
  });

  emailInput.on('input', function() {
    $(this).removeClass('is-invalid');
    $('#emailError').text('');
  });

  forgotPasswordForm.submit(async function(event) {
    event.preventDefault();
    clearMessages();

    try {
      if (!validateForm()) {
        return;
      }

      resetButton.prop("disabled", true);
      resetButton.text("Sending...");

      showMessage("Sending reset link...", false);

      const email = emailInput.val().trim();

      const result = await initiatePasswordReset(email);

      if (result.success) {
        showMessage(result.message, false, true);
        forgotPasswordForm[0].reset();

        emailInput.removeClass('is-invalid');
        $('#emailError').text('');
      }

    } catch (error) {
      console.error("Form submission error:", error);
      // Use enhanced error handling
      NotificationSystem.handleError(error, {
        operation: 'forgot-password',
        email: emailInput.val().trim()
      });
    } finally {
      resetButton.prop("disabled", false);
      resetButton.text("Send Reset Link");
    }
  });
}

$(document).ready(function() {
  // Check browser compatibility first
  if (!NotificationSystem.checkBrowserCompatibility()) {
    return; // Stop initialization if browser is incompatible
  }

  // Validate required configuration before initializing
  const requiredConfig = ['functionUrl'];
  if (NotificationSystem.validateConfiguration(requiredConfig)) {
    // Validate required DOM elements
    const requiredElements = ['#forgotPasswordForm', '#email', '#resetButton'];
    if (NotificationSystem.validateDOMElements(requiredElements)) {
      initializeFormHandlers();
    }
  }
});
