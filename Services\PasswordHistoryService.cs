using Azure.Storage.Blobs;
using Azure.Storage.Blobs.Models;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Text.RegularExpressions;
using PasswordHistoryValidator.Shared;

namespace PasswordHistoryValidator.Services;

public class PasswordHistoryService
{
    private readonly ILogger<PasswordHistoryService> _logger;
    private readonly BlobServiceClient _blobServiceClient;
    private readonly IMemoryCache _cache;
    private readonly JsonSerializerOptions _jsonOptions;
    private readonly int _maxHistoryCount;
    private readonly int _workFactor;

    private const string ContainerName = "passwordhistory";
    private readonly TimeSpan _cacheExpiration = TimeSpan.FromMinutes(15);

    // Compiled regex for efficient sanitization of application IDs
    private static readonly Regex InvalidCharsRegex = new(@"[/\\:.]+", RegexOptions.Compiled);

    public PasswordHistoryService(
        ILogger<PasswordHistoryService> logger,
        BlobServiceClient blobServiceClient,
        IMemoryCache cache,
        IOptions<PasswordHistoryOptions> passwordHistoryOptions,
        JsonSerializerOptions jsonOptions)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _blobServiceClient = blobServiceClient ?? throw new ArgumentNullException(nameof(blobServiceClient));
        _cache = cache ?? throw new ArgumentNullException(nameof(cache));
        _jsonOptions = jsonOptions ?? throw new ArgumentNullException(nameof(jsonOptions));

        var passwordHistoryOpts = passwordHistoryOptions.Value;
        _maxHistoryCount = passwordHistoryOpts.MaxCount;
        _workFactor = passwordHistoryOpts.WorkFactor;
    }


    // app-scoped methods only (single-tenant architecture)
    public async Task<(bool Success, List<string>? Data, string ErrorMessage)> GetPasswordHistoryAsync(string applicationId, string userId, CancellationToken cancellationToken = default)
    {
        // Normalize userId (email) for consistent storage and retrieval
        var normalizedUserId = Utilities.NormalizeEmail(userId);
        return await GetPasswordHistoryInternalAsync(normalizedUserId, applicationId, cancellationToken);
    }

    public async Task<(bool Success, bool Data, string ErrorMessage)> UpdatePasswordHistoryAsync(string applicationId, string userId, string newPassword, CancellationToken cancellationToken = default)
    {
        // Normalize userId (email) for consistent storage and retrieval
        var normalizedUserId = Utilities.NormalizeEmail(userId);
        return await UpdatePasswordHistoryInternalAsync(normalizedUserId, newPassword, applicationId, cancellationToken);
    }

    public async Task<(bool Success, bool Data, string ErrorMessage)> ValidatePasswordAgainstHistoryAsync(string applicationId, string userId, string password, CancellationToken cancellationToken = default)
    {
        // Normalize userId (email) for consistent storage and retrieval
        var normalizedUserId = Utilities.NormalizeEmail(userId);
        return await ValidatePasswordAgainstHistoryInternalAsync(normalizedUserId, password, applicationId, cancellationToken);
    }

    private async Task<(bool Success, List<string>? Data, string ErrorMessage)> GetPasswordHistoryInternalAsync(string userId, string? applicationId, CancellationToken cancellationToken)
    {
        return await ExecuteWithErrorHandling(async () =>
        {
            var scopedUserId = GetScopedUserId(userId, applicationId);
            var cacheKey = $"password_history_{scopedUserId}";

            if (_cache.TryGetValue(cacheKey, out List<string>? cachedHistory))
            {
                return (true, cachedHistory ?? new List<string>(), string.Empty);
            }

            var blobClient = await GetBlobClientAsync(scopedUserId, cancellationToken);

            if (!await blobClient.ExistsAsync(cancellationToken: cancellationToken))
            {
                var emptyHistory = new List<string>();
                _cache.Set(cacheKey, emptyHistory, TimeSpan.FromMinutes(5));
                return (true, emptyHistory, string.Empty);
            }

            var downloadResult = await blobClient.DownloadContentAsync(cancellationToken: cancellationToken);
            var historyData = JsonSerializer.Deserialize<PasswordHistoryStorage>(downloadResult.Value.Content.ToString(), _jsonOptions);
            var passwordHashes = historyData?.PasswordHashes ?? new List<string>();

            _cache.Set(cacheKey, passwordHashes, _cacheExpiration);
            return (true, passwordHashes, string.Empty);
        }, $"Error retrieving password history for user {userId}");
    }

    private async Task<(bool Success, bool Data, string ErrorMessage)> UpdatePasswordHistoryInternalAsync(string userId, string newPassword, string? applicationId, CancellationToken cancellationToken)
    {
        return await ExecuteWithErrorHandling(async () =>
        {
            var scopedUserId = GetScopedUserId(userId, applicationId);
            var historyResult = await GetPasswordHistoryInternalAsync(userId, applicationId, cancellationToken);
            if (!historyResult.Success)
            {
                return (false, false, historyResult.ErrorMessage);
            }

            var passwordHashes = historyResult.Data ?? new List<string>();
            string newPasswordHash = BCrypt.Net.BCrypt.HashPassword(newPassword, _workFactor);

            passwordHashes.Insert(0, newPasswordHash);
            if (passwordHashes.Count > _maxHistoryCount)
            {
                passwordHashes = passwordHashes.Take(_maxHistoryCount).ToList();
            }

            await SavePasswordHistoryAsync(scopedUserId, passwordHashes, cancellationToken);
            return (true, true, string.Empty);
        }, $"Error updating password history for user {userId}");
    }

    private async Task<(bool Success, bool Data, string ErrorMessage)> ValidatePasswordAgainstHistoryInternalAsync(string userId, string password, string? applicationId, CancellationToken cancellationToken)
    {
        return await ExecuteWithErrorHandling(async () =>
        {
            var historyResult = await GetPasswordHistoryInternalAsync(userId, applicationId, cancellationToken);
            if (!historyResult.Success)
            {
                return (false, false, historyResult.ErrorMessage);
            }

            var passwordHashes = historyResult.Data ?? new List<string>();
            foreach (var hash in passwordHashes)
            {
                if (!string.IsNullOrEmpty(hash) && BCrypt.Net.BCrypt.Verify(password, hash))
                {
                    _logger.LogWarning("Password reuse detected for user {userId}", userId);
                    return (false, false, "Password has been used recently. Please choose a different password.");
                }
            }

            return (true, true, string.Empty);
        }, $"Error validating password for user {userId}");
    }

    // helpers
    private async Task<BlobClient> GetBlobClientAsync(string userId, CancellationToken cancellationToken)
    {
        var containerClient = _blobServiceClient.GetBlobContainerClient(ContainerName);
        await containerClient.CreateIfNotExistsAsync(PublicAccessType.None, cancellationToken: cancellationToken);
        return containerClient.GetBlobClient($"{userId}.json");
    }

    private async Task SavePasswordHistoryAsync(string userId, List<string> passwordHashes, CancellationToken cancellationToken)
    {
        var blobClient = await GetBlobClientAsync(userId, cancellationToken);
        var historyData = new PasswordHistoryStorage
        {
            UserId = userId,
            LastUpdatedUtc = DateTime.UtcNow,
            PasswordHashes = passwordHashes
        };

        var jsonHistory = JsonSerializer.Serialize(historyData, _jsonOptions);
        using var stream = new MemoryStream(Encoding.UTF8.GetBytes(jsonHistory));

        var options = new BlobUploadOptions
        {
            Metadata = CreateBlobMetadata(passwordHashes.Count),
            HttpHeaders = new BlobHttpHeaders { ContentType = "application/json" }
        };

        await blobClient.DeleteIfExistsAsync(cancellationToken: cancellationToken);
        await blobClient.UploadAsync(stream, options, cancellationToken);

        var cacheKey = $"password_history_{userId}";
        _cache.Set(cacheKey, passwordHashes, _cacheExpiration);
    }

    private Dictionary<string, string> CreateBlobMetadata(int recordCount)
    {
        return new Dictionary<string, string>
        {
            { "lastUpdated", DateTime.UtcNow.ToString("o") },
            { "recordCount", recordCount.ToString() },
            { "correlationId", Guid.NewGuid().ToString() }
        };
    }

    private string GetScopedUserId(string userId, string? applicationId)
    {
        if (string.IsNullOrEmpty(applicationId))
            return userId;

        var sanitizedAppId = InvalidCharsRegex.Replace(applicationId, "").Trim();

        if (string.IsNullOrEmpty(sanitizedAppId))
            throw new ArgumentException("Application ID cannot be empty or contain only invalid characters", nameof(applicationId));

        return $"{sanitizedAppId}/{userId}";
    }

    private async Task<T> ExecuteWithErrorHandling<T>(Func<Task<T>> operation, string errorContext)
    {
        try
        {
            return await operation();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, errorContext);
            // Return appropriate tuple based on T type
            if (typeof(T) == typeof((bool Success, List<string>? Data, string ErrorMessage)))
            {
                return (T)(object)(false, (List<string>?)null, "An error occurred while processing the request");
            }
            else if (typeof(T) == typeof((bool Success, bool Data, string ErrorMessage)))
            {
                return (T)(object)(false, false, "An error occurred while processing the request");
            }
            throw; // Should not reach here with current usage
        }
    }
}

public class PasswordHistoryStorage
{
    [JsonPropertyName("userId")]
    public string UserId { get; set; } = string.Empty;

    [JsonPropertyName("lastUpdatedUtc")]
    public DateTime LastUpdatedUtc { get; set; }

    [JsonPropertyName("passwordHashes")]
    public List<string> PasswordHashes { get; set; } = new();
}
