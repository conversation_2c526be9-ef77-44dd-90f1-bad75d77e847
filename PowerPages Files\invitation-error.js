/**
 * Unified Notification System for Power Pages
 */
const NotificationSystem = {
  config: {
    defaultTimeout: 5000,
    fadeInDuration: 300,
    fadeOutDuration: 300,
    autoHide: true,
    showIcons: true
  },

  icons: {
    success: 'fas fa-check-circle',
    error: 'fas fa-exclamation-circle',
    warning: 'fas fa-exclamation-triangle',
    info: 'fas fa-info-circle',
    loading: 'fas fa-spinner fa-spin'
  },

  show: function(type, message, options = {}) {
    const settings = { ...this.config, ...options };
    let container = this.getNotificationContainer(settings.containerId);
    if (!container) {
      console.warn('Notification container not found');
      return;
    }

    if (settings.clearExisting !== false) {
      this.clear(container);
    }

    const notification = this.createNotification(type, message, settings);
    container.appendChild(notification);
    notification.classList.add('notification-fade-in');

    if (settings.autoHide && settings.timeout > 0) {
      setTimeout(() => {
        this.hide(notification);
      }, settings.timeout || this.config.defaultTimeout);
    }

    this.setAriaLiveRegion(notification, type);
    return notification;
  },

  showSuccess: function(message, options = {}) {
    return this.show('success', message, {
      ...options,
      timeout: options.timeout || 4000,
      title: options.title || 'Success'
    });
  },

  showError: function(message, options = {}) {
    return this.show('error', message, {
      ...options,
      timeout: options.timeout || 0,
      title: options.title || 'Error'
    });
  },

  showWarning: function(message, options = {}) {
    return this.show('warning', message, {
      ...options,
      timeout: options.timeout || 6000,
      title: options.title || 'Warning'
    });
  },

  showInfo: function(message, options = {}) {
    return this.show('info', message, {
      ...options,
      timeout: options.timeout || 5000,
      title: options.title || 'Information'
    });
  },

  showLoading: function(message, options = {}) {
    return this.show('loading', message, {
      ...options,
      timeout: 0,
      title: options.title || 'Loading',
      autoHide: false
    });
  },

  createNotification: function(type, message, settings) {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.setAttribute('role', type === 'error' ? 'alert' : 'status');
    notification.setAttribute('aria-live', type === 'error' ? 'assertive' : 'polite');

    let content = '';
    if (settings.showIcons && this.icons[type]) {
      content += `<div class="notification-icon"><i class="${this.icons[type]}" aria-hidden="true"></i></div>`;
    }

    content += '<div class="notification-content">';
    if (settings.title) {
      content += `<div class="notification-title">${this.escapeHtml(settings.title)}</div>`;
    }
    content += `<div class="notification-message">${this.escapeHtml(message)}</div>`;
    content += '</div>';

    notification.innerHTML = content;
    if (settings.compact) {
      notification.classList.add('notification-compact');
    }

    return notification;
  },

  getNotificationContainer: function(containerId) {
    if (containerId) {
      return document.getElementById(containerId);
    }

    const commonIds = ['notificationContainer', 'messageContainer', 'alertContainer'];
    for (const id of commonIds) {
      const container = document.getElementById(id);
      if (container) return container;
    }

    const jquerySelectors = ['#errorMessage', '#successMessage', '#messageContent'];
    for (const selector of jquerySelectors) {
      const element = $(selector);
      if (element.length > 0) {
        return element[0];
      }
    }

    return this.createDefaultContainer();
  },

  createDefaultContainer: function() {
    const container = document.createElement('div');
    container.id = 'notificationContainer';
    container.className = 'notification-container';

    const mainContent = document.querySelector('.card-body, .container, main, body');
    if (mainContent) {
      mainContent.insertBefore(container, mainContent.firstChild);
      return container;
    }

    return null;
  },

  hide: function(notification) {
    if (!notification || !notification.parentNode) return;
    notification.style.transition = `opacity ${this.config.fadeOutDuration}ms ease`;
    notification.style.opacity = '0';
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, this.config.fadeOutDuration);
  },

  clear: function(container) {
    if (!container) {
      container = this.getNotificationContainer();
    }

    if (container) {
      const notifications = container.querySelectorAll('.notification, .message, .alert');
      notifications.forEach(notification => {
        this.hide(notification);
      });
    }

    $('#errorMessage, #successMessage').hide();
  },

  setAriaLiveRegion: function(notification, type) {
    if (type === 'error') {
      notification.setAttribute('aria-live', 'assertive');
      notification.setAttribute('role', 'alert');
    } else {
      notification.setAttribute('aria-live', 'polite');
      notification.setAttribute('role', 'status');
    }
  },

  escapeHtml: function(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  },

  handleError: function(error, context = {}) {
    console.error('Error in context:', context, error);

    if (error.status) {
      return this.handleHttpError(error, context);
    }

    if (error instanceof TypeError && error.message.includes('fetch')) {
      return this.showError('Network connection failed. Please check your internet connection and try again.', {
        title: 'Connection Error',
        timeout: 0
      });
    }

    if (error.name === 'AbortError' || error.message.includes('timeout')) {
      return this.showError('Request timed out. Please check your connection and try again. If the problem persists, try refreshing the page.', {
        title: 'Request Timeout',
        timeout: 0
      });
    }

    if (error.message.includes('configuration') || error.message.includes('missing')) {
      return this.showError('System configuration error. Please contact support if this persists.', {
        title: 'Configuration Error',
        timeout: 0
      });
    }

    if (error.message.includes('rate limit') || error.message.includes('429')) {
      return this.showWarning(error.message, {
        title: 'Rate Limit',
        timeout: 10000
      });
    }

    if (error.message.includes('401') || error.message.includes('unauthorized')) {
      return this.showError('Authentication failed. Please refresh the page and try again.', {
        title: 'Authentication Error',
        timeout: 0
      });
    }

    const userMessage = error.message || 'An unexpected error occurred. Please try again.';
    return this.showError(userMessage, {
      title: 'Error',
      timeout: 0
    });
  },

  handleHttpError: function(error, context = {}) {
    const status = error.status;

    if (status >= 400 && status < 500) {
      switch (status) {
        case 400:
          return this.showError('Invalid request. Please check your input and try again.', {
            title: 'Invalid Request',
            timeout: 0
          });
        case 401:
          return this.showError('Authentication required. Please refresh the page and try again.', {
            title: 'Authentication Required',
            timeout: 0
          });
        case 403:
          return this.showError('Access denied. You do not have permission to perform this action.', {
            title: 'Access Denied',
            timeout: 0
          });
        case 404:
          return this.showError('Service not found. Please contact support if this persists.', {
            title: 'Service Unavailable',
            timeout: 0
          });
        case 409:
          const conflictMessage = error.message || 'A conflict occurred. Please review your input.';
          if (conflictMessage.toLowerCase().includes('email') &&
              (conflictMessage.toLowerCase().includes('exists') ||
               conflictMessage.toLowerCase().includes('already') ||
               conflictMessage.toLowerCase().includes('duplicate'))) {
            return this.showError('An account with this email address already exists. Please try signing in instead.', {
              title: 'Account Already Exists',
              timeout: 0
            });
          }
          return this.showError(conflictMessage, {
            title: 'Conflict',
            timeout: 0
          });
        case 429:
          return this.showWarning('Too many requests. Please wait a moment before trying again.', {
            title: 'Rate Limit Exceeded',
            timeout: 15000
          });
        default:
          return this.showError('Request failed. Please try again or contact support if the problem persists.', {
            title: 'Request Failed',
            timeout: 0
          });
      }
    }

    if (status >= 500) {
      return this.showError('A server error occurred. Please try again later or contact support.', {
        title: 'Server Error',
        timeout: 0
      });
    }

    return this.showError('An unexpected error occurred. Please try again.', {
      title: 'Unexpected Error',
      timeout: 0
    });
  },

  validateConfiguration: function(requiredConfig = []) {
    const missing = [];
    for (const configKey of requiredConfig) {
      if (!window.appConfig || !window.appConfig[configKey]) {
        missing.push(configKey);
      }
    }

    if (missing.length > 0) {
      this.showError(`Missing configuration: ${missing.join(', ')}. Please contact support.`, {
        title: 'Configuration Error',
        timeout: 0
      });
      return false;
    }

    return true;
  },

  validateDOMElements: function(elementSelectors = []) {
    const missing = [];
    for (const selector of elementSelectors) {
      const element = document.querySelector(selector);
      if (!element) {
        missing.push(selector);
      }
    }

    if (missing.length > 0) {
      console.error('Missing DOM elements:', missing);
      this.showError('Page elements missing. Please refresh the page.', {
        title: 'Page Error',
        timeout: 0
      });
      return false;
    }

    return true;
  },

  checkBrowserCompatibility: function() {
    const requiredFeatures = [];
    
    // Check for fetch API
    if (typeof fetch === 'undefined') {
      requiredFeatures.push('Fetch API');
    }
    
    // Check for Promise support
    if (typeof Promise === 'undefined') {
      requiredFeatures.push('Promise support');
    }
    
    // Check for modern JavaScript features
    if (typeof URLSearchParams === 'undefined') {
      requiredFeatures.push('URL Parameters');
    }
    
    // Check for localStorage/sessionStorage
    try {
      if (typeof localStorage === 'undefined' || typeof sessionStorage === 'undefined') {
        requiredFeatures.push('Local Storage');
      }
    } catch (e) {
      requiredFeatures.push('Local Storage');
    }
    
    // Check for modern DOM methods
    if (!document.querySelector || !document.querySelectorAll) {
      requiredFeatures.push('Modern DOM methods');
    }
    
    if (requiredFeatures.length > 0) {
      // Use basic DOM manipulation for compatibility message
      const message = `Your browser is missing required features: ${requiredFeatures.join(', ')}. Please update to a modern browser (Chrome 60+, Firefox 55+, Safari 12+, Edge 79+) to use this application.`;
      
      // Try to show error using NotificationSystem, fallback to alert
      try {
        this.showError(message, {
          title: 'Browser Compatibility Issue',
          timeout: 0
        });
      } catch (e) {
        // Fallback to basic alert if NotificationSystem fails
        alert('Browser Compatibility Issue: ' + message);
      }
      
      return false;
    }
    
    return true;
  },
};

document.addEventListener('DOMContentLoaded', function() {
    // Check browser compatibility first
    if (!NotificationSystem.checkBrowserCompatibility()) {
        return; // Stop initialization if browser is incompatible
    }
    
    initializeErrorPage();
    loadErrorDetails();
});

function initializeErrorPage() {
    if (!window.appConfig) {
        NotificationSystem.showError('System configuration missing. Please contact support.', {
            title: 'Configuration Error',
            timeout: 0
        });
        return;
    }
}

function loadErrorDetails() {
    try {
        const errorData = sessionStorage.getItem('invitationError');

        if (errorData) {
            const error = JSON.parse(errorData);
            displayErrorDetails(error);

            sessionStorage.removeItem('invitationError');
        } else {
            const urlParams = new URLSearchParams(window.location.search);
            const errorMessage = urlParams.get('error');
            const errorCode = urlParams.get('code');

            if (errorMessage || errorCode) {
                displayErrorDetails({
                    message: errorMessage || 'Unknown error',
                    code: errorCode,
                    source: 'url-parameters',
                    timestamp: new Date().toISOString()
                });
            }
        }
    } catch (error) {
        console.error('Error loading error details:', error);
    }
}

function displayErrorDetails(errorData) {
    console.log('Displaying error details:', errorData);

    if (errorData.message) {
        const errorMessage = document.getElementById('errorMessage');
        if (errorMessage) {
            const messageText = errorMessage.querySelector('p');
            if (messageText) {
                messageText.textContent = errorData.message;
            }
        }
    }

    updatePageTitle(errorData);
}

function updatePageTitle(errorData) {
    if (!errorData || !errorData.message) return;

    const message = errorData.message.toLowerCase();
    const errorMessage = document.querySelector('.error-message');
    const titleElement = errorMessage?.querySelector('h3');

    if (!titleElement) return;

    if (message.includes('expired')) {
        titleElement.textContent = 'Invitation Expired';
        const description = errorMessage.querySelector('p');
        if (description) {
            description.textContent = 'Your invitation link has expired. Please request a new invitation from your administrator.';
        }
    } else if (message.includes('used') || message.includes('already')) {
        titleElement.textContent = 'Invitation Already Used';
        const description = errorMessage.querySelector('p');
        if (description) {
            description.textContent = 'This invitation has already been used to create an account. If you already have an account, please try logging in.';
        }
    } else if (message.includes('invalid') || message.includes('not found')) {
        titleElement.textContent = 'Invalid Invitation';
        const description = errorMessage.querySelector('p');
        if (description) {
            description.textContent = 'The invitation link appears to be invalid or corrupted. Please check the link and try again, or request a new invitation.';
        }
    }
}

function sanitizeInput(input) {
    if (typeof input !== 'string') return '';
    return input.trim().replace(/[<>\"'&]/g, '').substring(0, 256);
}

function logUserAction(action, details = {}) {
    try {
        console.log('User action:', action, details);

    } catch (error) {
        console.error('Error logging user action:', error);
    }
}

logUserAction('invitation_error_page_view', {
    timestamp: new Date().toISOString(),
    userAgent: navigator.userAgent,
    referrer: document.referrer
});

if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        initializeErrorPage,
        setupEventListeners,
        loadErrorDetails,
        displayErrorDetails,
        updatePageTitle,
        sanitizeInput,
        logUserAction
    };
}
