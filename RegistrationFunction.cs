using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.Graph;
using Microsoft.Graph.Models;
using System.Net;
using System.Text.Json;
using System.ComponentModel.DataAnnotations;
using PasswordHistoryValidator.Services;
using PasswordHistoryValidator.Shared;

namespace PasswordHistoryValidator;

public class RegistrationFunction
{
    private readonly ILogger<RegistrationFunction> _logger;
    private readonly PasswordHistoryService _passwordHistoryService;
    private readonly RateLimitService _rateLimitService;
    private readonly GraphServiceClient _graphServiceClient;
    private readonly EntraExternalIDOptions _entraExternalIDOptions;
    private readonly InvitationTokenManager _invitationTokenManager;
    private readonly EmailService _emailService;
    private readonly JsonSerializerOptions _jsonOptions;

    public RegistrationFunction(
        ILogger<RegistrationFunction> logger,
        PasswordHistoryService passwordHistoryService,
        RateLimitService rateLimitService,
        GraphServiceClient graphServiceClient,
        IOptions<EntraExternalIDOptions> entraExternalIDOptions,
        InvitationTokenManager invitationTokenManager,
        EmailService emailService,
        JsonSerializerOptions jsonOptions)
    {
        _logger = logger;
        _passwordHistoryService = passwordHistoryService;
        _rateLimitService = rateLimitService;
        _graphServiceClient = graphServiceClient;
        _entraExternalIDOptions = entraExternalIDOptions.Value;
        _invitationTokenManager = invitationTokenManager;
        _emailService = emailService;
        _jsonOptions = jsonOptions;
    }

    [Function("RegistrationService")]
    public async Task<HttpResponseData> Run(
        [HttpTrigger(AuthorizationLevel.Function, "post", "options")] HttpRequestData req,
        CancellationToken cancellationToken)
    {
        var correlationId = Utilities.GenerateCorrelationId();

        // Handle CORS preflight requests
        if (req.Method.Equals("OPTIONS", StringComparison.OrdinalIgnoreCase))
        {
            return HttpResponseHelper.CreateCorsResponse(req);
        }

        try
        {
            var operation = req.Query["operation"];
            if (string.IsNullOrEmpty(operation))
            {
                return await HttpResponseHelper.CreateErrorResponse(req, "Operation parameter required", correlationId, _jsonOptions);
            }

            return operation switch
            {
                "register" => await HandleUserRegistration(req, correlationId, cancellationToken),
                _ => await HttpResponseHelper.CreateErrorResponse(req, $"Invalid operation: {operation}", correlationId, _jsonOptions)
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Registration service error [CorrelationId: {CorrelationId}]", correlationId);
            return await HttpResponseHelper.CreateErrorResponse(req, "Service error", correlationId, _jsonOptions);
        }
    }

    private async Task<HttpResponseData> HandleUserRegistration(HttpRequestData req, string correlationId, CancellationToken cancellationToken)
    {
        try
        {
            var invitationData = await JsonSerializer.DeserializeAsync<InvitationRequest>(req.Body, _jsonOptions, cancellationToken);
            if (invitationData == null)
                return await HttpResponseHelper.CreateErrorResponse(req, "Invalid request data", correlationId, _jsonOptions);

            if (string.IsNullOrEmpty(invitationData.VerificationCode))
                return await HttpResponseHelper.CreateErrorResponse(req, "Invitation code required for registration", correlationId, _jsonOptions);

            // Validate request data
            var validationResults = new List<ValidationResult>();
            var validationContext = new ValidationContext(invitationData);
            if (!Validator.TryValidateObject(invitationData, validationContext, validationResults, true))
            {
                var errors = string.Join(", ", validationResults.Select(r => r.ErrorMessage));
                return await HttpResponseHelper.CreateErrorResponse(req, $"Validation failed: {errors}", correlationId, _jsonOptions);
            }

            var (isValid, tokenData, errorMessage) = string.IsNullOrEmpty(invitationData.Token)
                ? await _invitationTokenManager.ValidateInvitationByCode(invitationData.VerificationCode)
                : await _invitationTokenManager.ValidateInvitationToken(invitationData.Token, invitationData.VerificationCode);

            if (!isValid || tokenData == null)
                return await HttpResponseHelper.CreateErrorResponse(req, errorMessage ?? "Invalid invitation", correlationId, _jsonOptions);

            if (!string.Equals(tokenData.Email, invitationData.Email, StringComparison.OrdinalIgnoreCase))
                return await HttpResponseHelper.CreateErrorResponse(req, "Email address does not match invitation", correlationId, _jsonOptions);

            // Create user data for registration
            var data = new AuthRequest
            {
                Email = invitationData.Email,
                Password = invitationData.Password ?? string.Empty,
                FirstName = invitationData.FirstName,
                LastName = invitationData.LastName,
                ApplicationName = tokenData.ApplicationId
            };

            // Rate limiting
            var rateLimitResponse = await HttpResponseHelper.CheckRateLimit(req, _rateLimitService, "register", correlationId, _jsonOptions, cancellationToken);
            if (rateLimitResponse != null)
                return rateLimitResponse;

            // Check if user already exists
            var existingUsers = await _graphServiceClient.Users
                .GetAsync(requestConfiguration =>
                {
                    var emailEsc = Utilities.EscapeODataString(data.Email);
                    requestConfiguration.QueryParameters.Filter =
                        $"mail eq '{emailEsc}' or userPrincipalName eq '{emailEsc}' or proxyAddresses/any(c:c eq 'SMTP:{emailEsc}')";
                    requestConfiguration.QueryParameters.Select = new[] { "id", "mail", "userPrincipalName", "displayName" };
                    requestConfiguration.QueryParameters.Top = 1;
                }, cancellationToken);

            if (existingUsers?.Value?.Any() == true)
                return await HttpResponseHelper.CreateErrorResponse(req, "User already exists", correlationId, _jsonOptions);

            // Validate password against history
            var passwordValidation = await _passwordHistoryService.ValidatePasswordAgainstHistoryAsync(
                data.ApplicationName ?? "Default Application", data.Email, data.Password, cancellationToken);

            if (!passwordValidation.Success)
                return await HttpResponseHelper.CreateErrorResponse(req, passwordValidation.ErrorMessage, correlationId, _jsonOptions);

            // Create user in Entra External ID
            var applicationName = data.ApplicationName ?? "Default Application";
            var newUser = CreateEntraUser(data, applicationName);

            var createdUser = await _graphServiceClient.Users.PostAsync(newUser, cancellationToken: cancellationToken);
            if (createdUser?.Id == null)
                return await HttpResponseHelper.CreateErrorResponse(req, "Failed to create user", correlationId, _jsonOptions);

            _logger.LogInformation("User created successfully: {UserId} for {Email} [CorrelationId: {CorrelationId}]",
                createdUser.Id, data.Email, correlationId);

            // Mark invitation token as used
            try
            {
                await _invitationTokenManager.MarkTokenAsUsed(invitationData.Token ?? string.Empty);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to mark invitation token as used for {Email} [CorrelationId: {CorrelationId}]", data.Email, correlationId);
            }

            await HandlePostCreationTasksAsync(data, invitationData, correlationId, cancellationToken);

            return await HttpResponseHelper.CreateJsonResponse(req, new { success = true, message = "User account created successfully" }, HttpStatusCode.OK, correlationId, _jsonOptions);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Registration error [CorrelationId: {CorrelationId}]", correlationId);
            return await HttpResponseHelper.CreateErrorResponse(req, "Registration failed", correlationId, _jsonOptions);
        }
    }

    private async Task HandlePostCreationTasksAsync(AuthRequest userData, InvitationRequest invitationData, string correlationId, CancellationToken cancellationToken)
    {
        // Send account created notification email
        try
        {
            var emailData = new AccountCreatedEmailData(
                userData.Email, userData.FirstName ?? "User", userData.ApplicationName ?? "Default Application");
            await _emailService.SendAccountCreatedNotificationAsync(emailData, correlationId, cancellationToken);
            _logger.LogInformation("Account created notification email sent to {Email} [CorrelationId: {CorrelationId}]", userData.Email, correlationId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending account created notification email to {Email} [CorrelationId: {CorrelationId}]", userData.Email, correlationId);
        }

        // Update password history
        try
        {
            var historyUpdate = await _passwordHistoryService.UpdatePasswordHistoryAsync(
                userData.ApplicationName ?? "Default Application", userData.Email, userData.Password, cancellationToken);
            
            if (!historyUpdate.Success)
            {
                _logger.LogWarning("Failed to update password history for user {Email}: {Error} [CorrelationId: {CorrelationId}]", 
                    userData.Email, historyUpdate.ErrorMessage, correlationId);
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to update password history for user {Email} [CorrelationId: {CorrelationId}]", userData.Email, correlationId);
        }
    }

    private User CreateEntraUser(AuthRequest data, string applicationName)
    {
        var upn = GenerateUserPrincipalName();
        var displayNameWithContext = CreateDisplayNameWithContext(
            data.FirstName ?? "Unknown",
            data.LastName ?? "User",
            applicationName);
        var identities = CreateUserIdentities(data.Email);
        var passwordProfile = CreatePasswordProfile(data.Password);

        return new User
        {
            DisplayName = displayNameWithContext,
            GivenName = data.FirstName,
            Surname = data.LastName,
            Mail = data.Email,
            UserPrincipalName = upn,
            Department = applicationName,
            Identities = identities,
            PasswordProfile = passwordProfile,
            AccountEnabled = true
        };
    }

    private string GenerateUserPrincipalName()
    {
        return $"{Guid.NewGuid()}@{_entraExternalIDOptions.DefaultDomain}";
    }

    private static string CreateDisplayNameWithContext(string firstName, string lastName, string applicationName)
    {
        return $"{firstName} {lastName} ({applicationName})";
    }

    private List<ObjectIdentity> CreateUserIdentities(string email)
    {
        return new List<ObjectIdentity>
        {
            new ObjectIdentity
            {
                SignInType = "emailAddress",
                Issuer = _entraExternalIDOptions.DefaultDomain,
                IssuerAssignedId = email
            }
        };
    }

    private static PasswordProfile CreatePasswordProfile(string password)
    {
        return new PasswordProfile
        {
            Password = password,
            ForceChangePasswordNextSignIn = false
        };
    }
}
